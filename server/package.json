{"name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "db:init": "node db/init.js", "db:migrate": "node db/migrate.js", "db:reset": "rm -f db/database.sqlite && node db/init.js", "test": "jest --config tests/jest.config.js", "test:watch": "jest --config tests/jest.config.js --watch", "test:coverage": "jest --config tests/jest.config.js --coverage"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.6.0", "better-sqlite3": "^12.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "node-cron": "^3.0.3"}}