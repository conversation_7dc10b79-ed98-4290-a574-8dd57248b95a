{"title": "Subscriptions", "add": "Add Subscription", "edit": "Edit Subscription", "delete": "Delete Subscription", "name": "Name", "amount": "Amount", "category": "Category", "paymentMethod": "Payment Method", "renewalDate": "Renewal Date", "status": "Status", "active": "Active", "inactive": "Inactive", "upcoming": "Upcoming", "expired": "Expired", "addNew": "Add New Subscription", "editDetails": "Edit Details", "confirmDelete": "Are you sure you want to delete this subscription?", "paymentHistory": "Payment History", "nextPayment": "Next Payment", "lastPayment": "Last Payment", "frequency": "Frequency", "notes": "Notes", "plan": "Plan", "billingCycle": "Billing Cycle", "startDate": "Start Date", "renewalType": "Renewal Type", "website": "Website", "today": "Today", "days": "days", "updated": "Subscription updated", "updateSuccess": "has been updated successfully.", "errorUpdate": "Error updating subscription", "failedUpdate": "Failed to update subscription", "added": "Subscription added", "addedSuccess": "has been added successfully.", "errorAdd": "Error adding subscription", "failedAdd": "Failed to add subscription", "deleted": "Subscription deleted", "deleteSuccess": "has been deleted.", "errorDelete": "Error deleting subscription", "failedDelete": "Failed to delete subscription", "statusUpdateError": "Error updating status", "statusUpdated": "Status updated", "statusUpdateSuccess": "has been updated.", "failedStatusUpdate": "Failed to update status", "renewed": "Subscription renewed successfully", "renewalError": "Error renewing subscription", "importFailed": "Import failed", "failedImport": "Failed to import subscriptions", "importSuccess": "Import successful", "exportSuccess": "Export successful", "loadingSubscriptions": "Loading subscriptions...", "manageAllServices": "Manage all your subscription services", "searchPlaceholder": "Search subscriptions...", "filterByCategory": "Filter by Category", "filterByBillingCycle": "Filter by Billing Cycle", "sortByNextBilling": "Sort by <PERSON> Billing Date", "all": "All", "trial": "Trial", "cancelled": "Cancelled", "noSubscriptionsFound": "No subscriptions found", "noResultsForFilters": "No results for your current filters. Try changing your search terms or filters.", "noSubscriptionsOfType": "You don't have any {{type}} subscriptions.", "getStartedMessage": "Get started by adding your first subscription.", "importSubscriptions": "Import Subscriptions", "exportToJson": "Your subscriptions have been exported to JSON.", "subscriptionsImported": "{{count}} subscriptions have been imported.", "renewedSuccessfully": "{{name}} has been renewed. Next billing date: {{date}}", "cannotUndo": "This action cannot be undone.", "details": {"subscriptionPlan": "Subscription Plan", "pricing": "Pricing", "amount": "Amount:", "billingCycle": "Billing Cycle:", "paymentDetails": "Payment Details", "paymentMethod": "Payment Method:", "renewalType": "Renewal Type:", "automatic": "Automatic", "manual": "Manual", "importantDates": "Important Dates", "startDate": "Start Date:", "lastPayment": "Last Payment:", "nextPayment": "Next Payment:", "category": "Category", "notes": "Notes", "website": "Website", "visitWebsite": "Visit Website", "editSubscription": "Edit Subscription", "renewNow": "Renew Now"}}