{"title": "Settings", "language": "Language", "theme": "Theme", "currency": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "Notifications", "general": "General", "appearance": "Appearance", "preferences": "Preferences", "account": "Account", "privacy": "Privacy", "security": "Security", "about": "About", "help": "Help", "logout": "Logout", "saveChanges": "Save Changes", "resetToDefault": "Reset to De<PERSON>ult", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemMode": "System Mode", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "languageChanged": "Language changed successfully", "settingsSaved": "Setting<PERSON> saved successfully", "categories": "Categories", "paymentMethods": "Payment Methods", "addCategory": "Add Category", "addPaymentMethod": "Add Payment Method", "editCategory": "Edit Category", "editPaymentMethod": "Edit Payment Method", "deleteCategory": "Delete Category", "deletePaymentMethod": "Delete Payment Method", "manageCategoriesDesc": "Manage subscription categories for better organization.", "managePaymentMethodsDesc": "Manage available payment methods for your subscriptions.", "editOptionTitle": "Edit {{type}}", "editOptionDesc": "Update the name for this {{type}}.", "addOptionTitle": "Add New {{type}}", "addOptionDesc": "Create a new {{type}} option.", "enterNamePlaceholder": "Enter {{type}} name...", "addOption": "Add {{type}}", "optionUpdated": "Option updated", "optionUpdateSuccess": "{{type}} option has been updated successfully.", "optionDeleted": "Option deleted", "optionDeleteSuccess": "{{type}} option has been deleted successfully.", "optionAdded": "Option added", "optionAddSuccess": "New {{type}} option has been added successfully.", "errorUpdatingOption": "Failed to update {{type}} option.", "errorDeletingOption": "Failed to delete {{type}} option.", "errorAddingOption": "Failed to add {{type}} option.", "deleteConfirmation": "Are you sure you want to delete \"{{name}}\"? Any subscriptions using this {{type}} will need to be updated.", "category": "category", "paymentMethod": "payment method", "loadingSettings": "Loading settings...", "generalSettings": "General Settings", "customizeGeneralPreferences": "Customize your general preferences", "themeMode": "Theme Mode", "selectTheme": "Select a theme", "light": "Light", "dark": "Dark", "system": "System", "chooseBetweenThemes": "Choose between light, dark, or system preference", "apiSynchronization": "API & Synchronization", "manageApiKeyDesc": "Manage your API key for backend synchronization. This key is stored locally.", "apiKey": "API Key", "enterApiKey": "Enter your API key", "apiKeyRequired": "The API key is required for creating, updating, or deleting subscriptions.", "saveApiKey": "Save API Key", "apiKeySaved": "API Key Saved", "apiKeySavedDesc": "Your new API key has been securely saved.", "dataManagement": "Data Management", "exportImportDesc": "Export your subscriptions or import from another service", "exportData": "Export Data", "importData": "Import Data", "resetData": "Reset Data", "resetDataDesc": "This will permanently delete all your subscriptions and settings. This action cannot be undone.", "resetAllData": "Reset All Data", "resetAllDataConfirm": "Are you sure you want to reset all data? This will permanently delete all subscriptions, payment history, and settings. This action cannot be undone.", "options": "Options", "data": "Data", "currencySettings": "<PERSON><PERSON><PERSON><PERSON>", "setPreferredCurrency": "Set your preferred currency for expense calculation", "defaultCurrency": "<PERSON><PERSON><PERSON>", "selectCurrency": "Select a currency", "preferredCurrencyDesc": "Your preferred currency for displaying subscription costs", "showInOriginalCurrency": "Show in original currency", "showOriginalCurrencyDesc": "Always display the original subscription currency alongside converted values", "exchangeRateStatus": "Exchange Rate Status", "automaticExchangeRateUpdates": "Automatic exchange rate updates and current status", "apiProvider": "API Provider", "apiConfiguration": "API Configuration", "configured": "Configured", "notConfigured": "Not configured", "updateFrequency": "Update Frequency", "dailyAutomatic": "Daily (Automatic)", "lastSuccessfulUpdate": "Last Successful Update", "justNow": "Just now", "apiKeyNotConfigured": "API key not configured. Automatic updates are disabled.", "updateNow": "Update Now", "refreshRates": "Refresh Rates", "currentExchangeRates": "Current Exchange Rates", "allRatesRelativeTo": "All rates are relative to {{currency}} (1 {{currency}} = X currency)", "noExchangeRatesAvailable": "No exchange rates available", "loadRates": "Load Rates", "chineseYuan": "Chinese Yuan", "usDollar": "US Dollar", "euro": "Euro", "britishPound": "British Pound", "canadianDollar": "Canadian Dollar", "australianDollar": "Australian Dollar", "japaneseYen": "Japanese Yen", "turkishLira": "Turkish Lira", "importSubscriptions": "Import Subscriptions", "uploadCsvOrJson": "Upload a CSV or JSON file to import multiple subscriptions at once.", "selectFile": "Select file", "validate": "Validate", "review": "Review", "complete": "Complete", "next": "Next", "validating": "Validating...", "validateFile": "Validate File", "unsupportedFileFormat": "Unsupported file format. Please upload a CSV or JSON file."}