{"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "confirm": "Confirm", "search": "Search", "filter": "Filter", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "yes": "Yes", "no": "No", "or": "or", "and": "and", "of": "of", "noData": "No data available", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "refresh": "Refresh", "import": "Import", "export": "Export", "reset": "Reset", "add": "Add", "remove": "Remove", "update": "Update", "create": "Create", "manage": "Manage", "settings": "Settings", "options": "Options", "general": "General", "data": "Data", "name": "Name", "amount": "Amount", "status": "Status", "category": "Category", "payment": "Payment", "paymentMethod": "Payment Method", "billing": "Billing", "billingCycle": "Billing Cycle", "subscription": "Subscription", "subscriptions": "Subscriptions", "dashboard": "Dashboard", "dashboardDescription": "Overview of your subscription expenses and activity", "reports": "Reports", "refreshing": "Refreshing...", "expenses": "Expenses", "renewals": "Renewals", "today": "Today", "tomorrow": "Tomorrow", "days": "days", "active": "Active", "trial": "Trial", "cancelled": "Cancelled", "monthly": "Monthly", "yearly": "Yearly", "quarterly": "Quarterly", "ascending": "Ascending", "descending": "Descending", "dataRefreshed": "Data refreshed", "dataRefreshedDesc": "Subscription data and renewals have been processed.", "refreshFailed": "Refresh failed", "refreshFailedDesc": "Failed to refresh data. Please try again.", "pickDate": "Pick a date", "selectBillingCycle": "Select billing cycle", "selectStatus": "Select status", "selectRenewalType": "Select renewal type", "automaticRenewal": "Automatic Renewal", "manualRenewal": "Manual Renewal", "unknownSize": "Unknown size", "kilobyte": "KB", "plan": "Plan", "startDate": "Start Date", "renewalType": "Renewal Type", "website": "Website", "notes": "Notes", "editSubscription": "Edit Subscription", "addNewSubscription": "Add New Subscription", "updateDetails": "Update your subscription details below", "enterDetails": "Enter the details of your subscription below", "monthlySpending": "Monthly Spending", "currentMonthExpenses": "Current month expenses", "yearlySpending": "Yearly Spending", "currentYearTotal": "Current year total expenses", "activeSubscriptions": "Active Subscriptions", "totalServices": "Total services", "paymentDate": "Payment date", "billingPeriodStart": "Billing period start", "billingPeriodEnd": "Billing period end", "paymentDateRequired": "Payment date is required", "currencyRequired": "Currency is required", "billingPeriodStartRequired": "Billing period start is required", "billingPeriodEndRequired": "Billing period end is required", "endDateAfterStart": "End date must be after start date", "statusRequired": "Status is required", "editPayment": "Edit Payment", "addPayment": "Add Payment", "updatePayment": "Update Payment", "paymentRecordCreated": "Payment record created successfully", "paymentRecordUpdated": "Payment record updated successfully", "paymentRecordDeleted": "Payment record deleted successfully", "deletePaymentRecord": "Delete Payment Record", "optionUpdated": "Option updated", "optionDeleted": "Option deleted", "deleteCategory": "Delete Category", "deletePaymentMethod": "Delete Payment Method", "optionAdded": "Option added", "selectPaymentDate": "Select payment date", "selectStartDate": "Select start date", "selectEndDate": "Select end date", "amountGreaterThanZero": "Amount must be greater than 0", "amountNonNegative": "Amount must be non-negative (0 or greater)", "succeeded": "Succeeded", "failed": "Failed", "refunded": "Refunded", "pending": "Pending", "optionalNotes": "Optional notes about this payment...", "additionalInformation": "Any additional information...", "planPlaceholder": "e.g., Premium, Family, Basic...", "websitePlaceholder": "https://example.com", "searchCurrency": "Search currency...", "noCurrencyFound": "No currency found.", "searchPaymentMethod": "Search payment method...", "noPaymentMethodFound": "No payment method found.", "searchCategory": "Search category...", "noCategoryFound": "No category found.", "selectCategory": "Select category...", "selectPaymentMethod": "Select payment method...", "unknownCategory": "Unknown category", "unknownPaymentMethod": "Unknown payment method", "categories": "Categories", "paymentMethods": "Payment Methods", "currency": "<PERSON><PERSON><PERSON><PERSON>", "somethingWentWrong": "Something went wrong", "tryAgain": "Try again", "unexpectedError": "An unexpected error occurred", "invalidRequest": "Invalid request. Please check your input.", "authRequired": "Authentication required. Please check your API key.", "noPermission": "You do not have permission to perform this action.", "notFound": "The requested resource was not found.", "conflict": "Conflict with existing data.", "invalidData": "Invalid data provided.", "tooManyRequests": "Too many requests. Please try again later.", "serverError": "Server error. Please try again later.", "serviceUnavailable": "Service temporarily unavailable.", "serviceMaintenance": "Service maintenance in progress.", "details": "Details", "records": "records", "searchPayments": "Search payments...", "paid": "Paid", "billingPeriod": "Billing Period", "deletePayment": "Delete Payment", "loadingPayments": "Loading payments...", "retry": "Retry", "noPaymentsFoundSearch": "No payments found matching your search", "noPaymentRecordsFound": "No payment records found"}