# Production Environment Configuration
# Copy this file to .env and update with your values

# ==================== REQUIRED CONFIGURATION ====================
# API key for authenticating requests to the server
# Generate a secure key: openssl rand -hex 32
API_KEY=your_secure_api_key_here


# ==================== OPTIONAL CONFIGURATION ====================
# Uncomment and modify only if you need to override defaults

# Server port (default: 3001)
# PORT=3001

# Base currency for the system (default: CNY)
# Supported: USD, EUR, GBP, CNY, JPY, CAD, AUD, TRY
# BASE_CURRENCY=CNY

# TianAPI key for real-time exchange rates (optional)
# If not set, the system will use built-in exchange rates
# Get your key from: https://www.tianapi.com/
# TIANAPI_KEY=your_tianapi_key_here

# Database path (default: /app/data/database.sqlite in Docker)
# Usually you don't need to change this
# DATABASE_PATH=/app/data/database.sqlite