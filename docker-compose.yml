version: '3.8'

services:
  subscription-manager:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: subscription-manager
    ports:
      - "${PORT:-3001}:${PORT:-3001}"
    volumes:
      - subscription-data:/app/data
    environment:
      - API_KEY=${API_KEY}
      - NODE_ENV=production
      - PORT=${PORT:-3001}
      - BASE_CURRENCY=${BASE_CURRENCY:-CNY}
      - TIANAPI_KEY=${TIANAPI_KEY}
      - DATABASE_PATH=${DATABASE_PATH:-/app/data/database.sqlite}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "const http=require('http');http.get('http://localhost:${PORT:-3001}/api/health',res=>process.exit(res.statusCode===200?0:1)).on('error',()=>process.exit(1));"]
      interval: 30s
      timeout: 3s
      retries: 3

volumes:
  subscription-data:
    driver: local