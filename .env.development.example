# Development Environment Configuration
# Copy this file to .env for local development

# ==================== SERVER CONFIGURATION ====================
# API key for development (use a simple key for local dev)
API_KEY=dev_api_key_123456
PORT=3001

# ==================== CURRENCY CONFIGURATION ====================
# Base currency for the system (default: CNY)
# Supported: USD, EUR, GBP, CNY, JPY, CAD, AUD, TRY
BASE_CURRENCY=CNY

# ==================== EXCHANGE RATE CONFIGURATION ====================
# TianAPI key for real-time exchange rates (optional)
# If not set, the system will use built-in exchange rates
# TIANAPI_KEY=your_tianapi_key_here

# ==================== LOGGING CONFIGURATION ====================
# Log levels for development
# Available levels: debug, info, warn, error
LOG_LEVEL=info         # Server-side logging
VITE_LOG_LEVEL=info    # Client-side logging

# ==================== DATABASE CONFIGURATION ====================
# Database path (optional, has smart defaults)
# DATABASE_PATH=./server/db/database.sqlite
