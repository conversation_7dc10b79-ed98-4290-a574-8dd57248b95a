# Dependencies
node_modules/
server/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Development files
.git/
.gitignore
.gitattributes
.github/

# Environment files (for security, .env files should not be copied to image)
.env
.env.*
!.env.example
!.env.production.example
server/.env
server/.env.*

# Build outputs (dist is needed for frontend build, but we exclude it since we copy from builder stage)
dist/
build/
out/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.editorconfig

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Test files
test/
tests/
__tests__/
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx
jest.config.*
vitest.config.*

# Documentation
docs/
*.md
!README.md

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Database files (should be handled by volumes)
server/db/database.sqlite
server/db/*.db
server/db/*.sqlite
server/db/*.sqlite3

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/

# ESLint cache
.eslintcache

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test
.env.production

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Backup files
*.backup
*.bak
*.orig

# Lock files (we want to keep them for reproducible builds)
# package-lock.json and yarn.lock should be included

# Development scripts (but keep server/start.sh for production)
dev.sh
build.sh

# Miscellaneous
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local